// This edge function is deprecated - video processing now happens client-side
// using the VideoPreProcessor class with real TensorFlow.js BlazePose detection

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Parse request body (not used since this endpoint is deprecated)
    await req.json();

    // This edge function is deprecated - video processing now happens client-side
    // using the VideoPreProcessor class with real TensorFlow.js BlazePose detection
    //
    // The main application uses:
    // 1. VideoPreProcessor.processUploadedVideo() for real pose detection
    // 2. Direct Supabase storage for pose data
    // 3. SkeletalOverlay component for visualization
    //
    // This function remains for backward compatibility but should not be used
    // for new video analysis workflows.

    return new Response(
      JSON.stringify({
        status: 'deprecated',
        message: 'This endpoint is deprecated. Video processing now happens client-side with real TensorFlow.js pose detection.',
        timestamp: new Date().toISOString(),
        redirectTo: 'Use VideoPreProcessor class directly'
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 410 // Gone - indicates the resource is no longer available
      }
    );
  } catch (error) {
    console.error('Error:', error);

    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  }
});