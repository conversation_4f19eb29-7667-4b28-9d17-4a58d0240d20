/**
 * FIXED ANALYSIS DISPLAY WITH DYNAMIC TRACKING
 * 
 * This replaces the broken AnalysisDisplay that used static database coordinates
 * with a dynamic version that uses real-time pose tracking.
 * 
 * Key changes:
 * 1. Uses FixedSkeletalOverlay instead of the broken SkeletalOverlay
 * 2. Implements proper real-time tracking
 * 3. Removes dependency on pre-stored database coordinates
 * 4. Adds dynamic tracking status indicators
 */

import React, { useState, useRef, useEffect } from 'react';
import { ActivityType, AnalysisResults } from '../types';
import { Play, Pause, RotateCcw, AlertCircle, Activity } from 'lucide-react';
import MetricCard from './MetricCard';
import FixedSkeletalOverlay from './FixedSkeletalOverlay'; // CRITICAL: Use fixed overlay
import { motion } from 'framer-motion';
import { validateVideoFormat } from '../utils/videoValidator';

interface FixedAnalysisDisplayProps {
  activity: ActivityType;
  results: AnalysisResults | null;
  uploadedVideos: Record<string, string>;
}

const FixedAnalysisDisplay: React.FC<FixedAnalysisDisplayProps> = ({
  activity,
  results,
  uploadedVideos
}) => {
  console.log('🚀 FixedAnalysisDisplay rendered with dynamic tracking');

  // Bail out early if no results
  if (!results) {
    console.error('No analysis results provided to FixedAnalysisDisplay');
    return <div className="text-red-500 p-4 text-center">Error: No analysis results available</div>;
  }

  const [currentView, setCurrentView] = useState<'side' | 'rear'>('side');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dynamicTrackingStatus, setDynamicTrackingStatus] = useState<string>('Initializing...');
  const videoRef = useRef<HTMLVideoElement>(null);

  // Get video source
  const videoSource = uploadedVideos[currentView];

  console.log('🎯 FixedAnalysisDisplay - Current view:', currentView);
  console.log('🎯 FixedAnalysisDisplay - Video source:', videoSource);

  // Bail out if no video source
  if (!videoSource) {
    console.error('No video source found for view:', currentView);
    return <div className="text-red-500 p-4 text-center">No video available for {currentView} view</div>;
  }

  // Dynamic tracking status monitor
  useEffect(() => {
    const statusInterval = setInterval(() => {
      if (videoRef.current && videoRef.current.readyState >= 2) {
        setDynamicTrackingStatus('Real-time tracking active');
      } else {
        setDynamicTrackingStatus('Waiting for video...');
      }
    }, 1000);

    return () => clearInterval(statusInterval);
  }, []);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedMetadata = async () => {
      try {
        // Check if video source is valid
        if (!video.src) {
          setError('No video source provided');
          setIsLoading(false);
          return;
        }

        console.log(`✅ Video loaded for dynamic tracking: ${video.src}`);
        console.log(`📏 Video dimensions: ${video.videoWidth}x${video.videoHeight}`);
        console.log(`⏱️ Video duration: ${video.duration}s`);

        // Enhanced validation for dynamic tracking
        if (video.videoWidth === 0 || video.videoHeight === 0) {
          setError('Invalid video dimensions. The video may be corrupted.');
          setIsLoading(false);
          return;
        }

        // Set duration and complete loading
        setDuration(video.duration);
        setIsLoading(false);
        setError(null);
        setDynamicTrackingStatus('Video ready for real-time tracking');
      } catch (err) {
        console.error('Video loading error:', err);
        setError('Error loading video. Please try again.');
        setIsLoading(false);
      }
    };

    const handleError = (e: Event) => {
      const videoElement = e.target as HTMLVideoElement;
      let errorMessage = 'An error occurred while loading the video';

      if (videoElement.error) {
        console.error('Video error code:', videoElement.error.code);
        console.error('Video error message:', videoElement.error.message);

        switch (videoElement.error.code) {
          case MediaError.MEDIA_ERR_ABORTED:
            errorMessage = 'Video playback was aborted';
            break;
          case MediaError.MEDIA_ERR_NETWORK:
            errorMessage = 'A network error occurred while loading the video. Please check your internet connection and try again.';
            break;
          case MediaError.MEDIA_ERR_DECODE:
            errorMessage = 'The video file is corrupted or uses an unsupported codec. Try converting your video to MP4 with H.264 encoding.';
            break;
          case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
            errorMessage = 'The video format is not supported by your browser. Supported formats are:\n\n' +
                         '• MP4 (H.264 codec)\n' +
                         '• WebM (VP8/VP9 codec)\n' +
                         '• MOV (H.264 codec)\n' +
                         '• AVI (with proper codec)';
            break;
          default:
            errorMessage = videoElement.error.message || 'An unknown error occurred while loading the video';
        }
      }

      console.error('Video loading error:', errorMessage);
      setError(errorMessage);
      setIsLoading(false);
      setDynamicTrackingStatus('Error - tracking unavailable');
    };

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
    };

    // Clean up any existing event listeners before adding new ones
    video.removeEventListener('loadedmetadata', handleLoadedMetadata);
    video.removeEventListener('error', handleError);
    video.removeEventListener('timeupdate', handleTimeUpdate);

    // Add event listeners
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('error', handleError);
    video.addEventListener('timeupdate', handleTimeUpdate);

    // Reset video state when source changes
    setIsLoading(true);
    setError(null);
    setDynamicTrackingStatus('Loading video...');

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('error', handleError);
      video.removeEventListener('timeupdate', handleTimeUpdate);
    };
  }, [currentView, uploadedVideos]);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const playVideo = async () => {
      try {
        // Check if the video is ready to play
        if (video.readyState < 2) {
          console.log('Video not ready to play yet (readyState =', video.readyState, ')');
          
          setTimeout(() => {
            if (isPlaying) {
              console.log('Retrying playback...');
              playVideo();
            }
          }, 500);
          return;
        }

        console.log('🎬 Starting video playback for dynamic tracking...');
        await video.play();
        console.log('✅ Video playing successfully - dynamic tracking active');
        setDynamicTrackingStatus('Real-time tracking active');
      } catch (error) {
        console.error('Error playing video:', error);

        // Try with more permissive settings
        try {
          console.log('Trying with more permissive playback settings');
          video.muted = true;
          video.playsInline = true;
          
          await new Promise(resolve => setTimeout(resolve, 500));
          await video.play();
          console.log('✅ Video playing with permissive settings');
          setDynamicTrackingStatus('Real-time tracking active');
        } catch (secondError) {
          console.error('Failed to play video even with permissive settings:', secondError);
          setIsPlaying(false);
          setError('Unable to play the video. Try clicking the play button or check if your browser supports this video format.');
          setDynamicTrackingStatus('Playback failed - tracking unavailable');
        }
      }
    };

    if (isPlaying) {
      playVideo();
    } else {
      console.log('⏸️ Pausing video - tracking paused');
      video.pause();
      setDynamicTrackingStatus('Video paused - tracking paused');
    }

    return () => {
      if (video) {
        console.log('Cleanup: pausing video');
        video.pause();
      }
    };
  }, [isPlaying, videoSource]);

  const metrics = Object.entries(results.metrics).map(([key, metric]: [string, any]) => ({
    id: key,
    ...metric
  }));

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (videoRef.current) {
      const time = parseFloat(e.target.value);
      videoRef.current.currentTime = time;
      setCurrentTime(time);
      console.log(`🎯 Seeking to ${time.toFixed(2)}s - dynamic tracking will update`);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const renderError = () => {
    if (!error) return null;

    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75 p-6"
      >
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg max-w-md">
          <div className="flex items-start">
            <AlertCircle className="w-6 h-6 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Video Error
              </h3>
              <p className="text-gray-600 dark:text-gray-300 whitespace-pre-line mb-4">{error}</p>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <p className="font-medium mb-2">Dynamic Tracking Requirements:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>MP4 format with H.264 encoding</li>
                  <li>Clear view of person for pose detection</li>
                  <li>Good lighting conditions</li>
                  <li>Person should be clearly visible in frame</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="grid lg:grid-cols-5 gap-6">
        <div className="lg:col-span-3">
          <div className="analysis-container bg-white dark:bg-gray-800 p-4 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 flex items-center">
                <Activity className="w-5 h-5 mr-2 text-green-500" />
                {activity === 'running' ? 'Running' : 'Cycling'} Analysis (Dynamic Tracking)
              </h2>

              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentView('side')}
                  className={`px-3 py-1 text-sm rounded-md ${
                    currentView === 'side'
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                  }`}
                >
                  Side View
                </button>
                <button
                  onClick={() => setCurrentView('rear')}
                  className={`px-3 py-1 text-sm rounded-md ${
                    currentView === 'rear'
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                  }`}
                >
                  Rear View
                </button>
              </div>
            </div>

            {/* Dynamic tracking status indicator */}
            <div className="mb-3 p-2 bg-green-50 dark:bg-green-900/20 rounded-md border border-green-200 dark:border-green-800">
              <div className="flex items-center text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                <span className="text-green-700 dark:text-green-300 font-medium">
                  Dynamic Tracking: {dynamicTrackingStatus}
                </span>
              </div>
            </div>

            <div className="relative bg-gray-900 rounded-md overflow-hidden mb-4" style={{ aspectRatio: '9/16', maxHeight: '70vh', minHeight: '400px' }}>
              {isLoading && (
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent mb-4"></div>
                  <div className="text-white text-center">
                    <p className="text-lg font-medium">Initializing Dynamic Tracking</p>
                    <p className="text-sm opacity-75">Preparing real-time pose analysis...</p>
                  </div>
                </div>
              )}

              {renderError()}

              <video
                ref={videoRef}
                className="w-full h-full object-contain"
                loop
                playsInline
                webkit-playsinline="true"
                x-webkit-airplay="allow"
                autoPlay={false}
                muted={true}
                controls={false}
                preload="auto"
                crossOrigin="anonymous"
                src={videoSource}
                onLoadStart={() => {
                  setIsLoading(true);
                  setDynamicTrackingStatus('Loading video...');
                }}
                onCanPlay={() => {
                  setIsLoading(false);
                  setDynamicTrackingStatus('Video ready for tracking');
                }}
                onError={(e) => {
                  console.error('Video loading error:', e);
                  setError('Error loading video. Please try refreshing the page.');
                  setIsLoading(false);
                  setDynamicTrackingStatus('Error - tracking unavailable');
                }}
              >
                <p>Your browser doesn't support HTML5 video. Please use a modern browser.</p>
              </video>

              {/* CRITICAL: Use FixedSkeletalOverlay instead of broken SkeletalOverlay */}
              <FixedSkeletalOverlay
                activity={activity}
                view={currentView}
                currentTime={currentTime}
                duration={duration}
                videoRef={videoRef}
                sessionId={results.sessionId}
              />

              <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 px-4 py-2">
                <div className="flex items-center justify-between text-white">
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => setIsPlaying(!isPlaying)}
                      className="hover:text-primary transition"
                      disabled={isLoading || !!error}
                    >
                      {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
                    </button>

                    <span className="text-sm">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </span>
                  </div>

                  <div className="flex-1 mx-4">
                    <input
                      type="range"
                      min="0"
                      max={duration || 100}
                      value={currentTime}
                      onChange={handleSeek}
                      className="w-full"
                      disabled={isLoading || !!error}
                    />
                  </div>

                  <button
                    onClick={() => {
                      if (videoRef.current) {
                        videoRef.current.currentTime = 0;
                        console.log('🔄 Reset to start - dynamic tracking will update');
                      }
                    }}
                    className="hover:text-primary transition"
                    disabled={isLoading || !!error}
                  >
                    <RotateCcw className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="analysis-container bg-white dark:bg-gray-800 p-6 mb-6"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
                Overall Score
              </h3>
              <div className="relative h-20 w-20">
                <svg className="w-full h-full" viewBox="0 0 36 36">
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#E5E7EB"
                    strokeWidth="3"
                    strokeDasharray="100, 100"
                  />
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke={results.overallScore >= 80 ? '#35AE7C' : '#FF7846'}
                    strokeWidth="3"
                    strokeDasharray={`${results.overallScore}, 100`}
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center text-xl font-bold">
                  {results.overallScore}%
                </div>
              </div>
            </div>

            <div className="space-y-4">
              {metrics.map((metric) => (
                <MetricCard key={metric.id} metric={metric} />
              ))}
            </div>
          </motion.div>
        </div>
      </div>

      <div className="analysis-container bg-white dark:bg-gray-800 p-6">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">
          Dynamic Analysis Summary
        </h3>

        <div className="space-y-4 text-gray-600 dark:text-gray-300">
          {activity === 'running' ? (
            <>
              <p>
                Your running form is being analyzed with <span className="font-semibold text-green-600 dark:text-green-400">
                real-time dynamic tracking</span> showing a score of {results.overallScore}%.
                The system tracks your movement across the video and scales the skeleton overlay
                to match your position and distance from the camera.
              </p>

              <p>Dynamic tracking observations:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Real-time pose detection follows your movement throughout the video</li>
                <li>Skeleton scaling adapts to your distance from the camera</li>
                <li>Joint angles are calculated from live detection data</li>
                <li>Center of mass tracking provides accurate positioning</li>
              </ul>

              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                  🎯 Dynamic Tracking Benefits:
                </h4>
                <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <li>• Skeleton follows your actual movement in real-time</li>
                  <li>• Scales automatically with distance changes</li>
                  <li>• Uses live detection instead of pre-stored coordinates</li>
                  <li>• Provides accurate joint angle measurements</li>
                </ul>
              </div>
            </>
          ) : (
            <>
              <p>
                Your cycling posture is being analyzed with <span className="font-semibold text-green-600 dark:text-green-400">
                real-time dynamic tracking</span> showing a score of {results.overallScore}%.
              </p>

              <p>Dynamic tracking observations:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Live pose detection tracks your position throughout the video</li>
                <li>Dynamic scaling adjusts to camera distance changes</li>
                <li>Real-time joint angle calculations</li>
                <li>Accurate center of mass tracking</li>
              </ul>
            </>
          )}

          <p className="italic text-sm mt-6 text-gray-500 dark:text-gray-400">
            Note: This analysis uses advanced real-time pose tracking. For the most comprehensive 
            biomechanical assessment, consider consulting with a professional coach or therapist.
          </p>
        </div>
      </div>
    </div>
  );
};

export default FixedAnalysisDisplay;