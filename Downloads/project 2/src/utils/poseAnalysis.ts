import * as poseDetection from '@tensorflow-models/pose-detection';
import * as tf from '@tensorflow/tfjs';
import '@tensorflow/tfjs-backend-webgl';

/**
 * CRITICAL FIXES: BlazePose Coordinate System & Enhanced Configuration
 *
 * 1. COORDINATE NORMALIZATION FIX:
 * BlazePose ALWAYS returns coordinates in pixel space, never normalized (0-1) coordinates.
 * This is true regardless of confidence scores being between 0-1.
 *
 * Previous bug: The normalizeCoordinates function incorrectly assumed that coordinates
 * between 0-1 were already normalized, causing skeletal overlays to misalign.
 *
 * Fixed: Always treat BlazePose coordinates as pixels and normalize using:
 * normalizedCoordinate = (pixelCoordinate / videoDimension) * 100
 *
 * 2. ENHANCED BLAZEPOSE CONFIGURATION:
 * Added missing configuration options to estimatePoses() for improved detection:
 * - maxPoses: 1 (single-person detection for running analysis)
 * - flipHorizontal: false (correct orientation for side-view videos)
 * - timestamp: video.currentTime * 1000000 (enables temporal smoothing for professional tracking)
 *
 * These enhancements provide:
 * - Improved pose detection stability and accuracy
 * - Smoother skeletal overlay animations during video playback
 * - Reduced jitter in joint tracking for medical-grade visualization
 * - Better temporal consistency between consecutive video frames
 */

export interface JointAngle {
  angle: number;
  position: { x: number; y: number };
}

export interface EnhancedJointData {
  angle: number;
  position: { x: number; y: number };
  confidence: number;
  detected: boolean;
}

export interface PoseAnalysis {
  angles: {
    hip: JointAngle;
    knee: JointAngle;
    ankle: JointAngle;
    trunk: JointAngle;
    neck: JointAngle;
    // Enhanced anatomical angles
    shoulder?: EnhancedJointData;
    elbow?: EnhancedJointData;
    wrist?: EnhancedJointData;
  };
  keypoints: {
    // Core detected joints
    hip: { x: number; y: number; confidence: number };
    knee: { x: number; y: number; confidence: number };
    ankle: { x: number; y: number; confidence: number };
    trunk: { x: number; y: number; confidence: number };
    neck: { x: number; y: number; confidence: number };
    // Enhanced keypoints from BlazePose
    shoulder?: { x: number; y: number; confidence: number };
    elbow?: { x: number; y: number; confidence: number };
    wrist?: { x: number; y: number; confidence: number };
    heel?: { x: number; y: number; confidence: number };
    foot?: { x: number; y: number; confidence: number };
    // Bilateral keypoints
    shoulderLeft?: { x: number; y: number; confidence: number };
    shoulderRight?: { x: number; y: number; confidence: number };
    elbowLeft?: { x: number; y: number; confidence: number };
    elbowRight?: { x: number; y: number; confidence: number };
    wristLeft?: { x: number; y: number; confidence: number };
    wristRight?: { x: number; y: number; confidence: number };
    hipLeft?: { x: number; y: number; confidence: number };
    hipRight?: { x: number; y: number; confidence: number };
    kneeLeft?: { x: number; y: number; confidence: number };
    kneeRight?: { x: number; y: number; confidence: number };
    ankleLeft?: { x: number; y: number; confidence: number };
    ankleRight?: { x: number; y: number; confidence: number };
  };
  metrics: {
    strideLength: number;
    footStrike: string;
    postureScore: number;
    detectionQuality: number;
    bilateralSymmetry?: number;
  };
}

export interface WorldLandmark3D {
  x: number;  // X position in meters
  y: number;  // Y position in meters
  z: number;  // Z depth in meters (distance from camera)
  confidence: number;
}

export interface PoseAnalysis3D extends PoseAnalysis {
  worldLandmarks: {
    hip: WorldLandmark3D;
    knee: WorldLandmark3D;
    ankle: WorldLandmark3D;
    shoulder: WorldLandmark3D;
    elbow: WorldLandmark3D;
    wrist: WorldLandmark3D;
    neck: WorldLandmark3D;
    heel: WorldLandmark3D;
    foot: WorldLandmark3D;
    // Bilateral landmarks
    hipLeft: WorldLandmark3D;
    hipRight: WorldLandmark3D;
    kneeLeft: WorldLandmark3D;
    kneeRight: WorldLandmark3D;
    ankleLeft: WorldLandmark3D;
    ankleRight: WorldLandmark3D;
  };
  estimatedDistanceMeters: number;  // Average Z-depth in meters
  depthScaleFactor: number;  // Scale factor based on distance
  pixelsPerMeter: number;  // Conversion factor for this frame
  heightCalibrated: boolean;  // Whether height was used for calibration
}

let detector: poseDetection.PoseDetector | null = null;
let isInitializing = false;
const maxRetries = 3;
const initialRetryDelay = 1000; // Start with 1 second

function getExponentialDelay(retryCount: number): number {
  return Math.min(initialRetryDelay * Math.pow(2, retryCount), 10000); // Cap at 10 seconds
}

export async function initializePoseDetection(retryCount = 0): Promise<poseDetection.PoseDetector> {
  if (detector) return detector;
  if (isInitializing) {
    // Wait for existing initialization to complete
    await new Promise(resolve => setTimeout(resolve, 100));
    return initializePoseDetection(retryCount);
  }

  try {
    isInitializing = true;

    // Ensure TensorFlow is ready and WebGL backend is properly initialized
    await tf.ready();

    // Explicitly set and initialize the WebGL backend
    await tf.setBackend('webgl');

    // Wait for backend initialization
    await tf.ready();

    // Log backend status
    console.log('TensorFlow.js backend:', tf.getBackend());

    // Create detector with enhanced TFJS model configuration for professional analysis
    // Use 'full' model for maximum keypoint detection accuracy with potential 3D capabilities
    try {
      detector = await poseDetection.createDetector(
        poseDetection.SupportedModels.BlazePose,
        {
          runtime: 'tfjs', // TFJS runtime for browser compatibility
          modelType: 'full', // Enhanced from 'lite' to 'full' for better anatomical keypoint detection
          enableSmoothing: true, // Enable smoothing for better temporal consistency in professional analysis
          enableSegmentation: false // Keep disabled for performance
        }
      );
      console.log('✅ Enhanced BlazePose detector created successfully with full model and optimized 3D configuration');
    } catch (blazeError) {
      console.warn('BlazePose failed, trying MoveNet as fallback:', blazeError);
      // Fallback to MoveNet if BlazePose fails
      detector = await poseDetection.createDetector(
        poseDetection.SupportedModels.MoveNet,
        {
          modelType: poseDetection.movenet.modelType.SINGLEPOSE_LIGHTNING
        }
      );
      console.log('MoveNet detector created as fallback');
    }

    isInitializing = false;
    return detector;
  } catch (error) {
    isInitializing = false;
    console.error('Error initializing pose detection:', error);

    if (retryCount < maxRetries) {
      const delay = getExponentialDelay(retryCount);
      console.log(`Retrying initialization (attempt ${retryCount + 1}/${maxRetries}) after ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return initializePoseDetection(retryCount + 1);
    }

    throw new Error(`Failed to initialize pose detection after ${maxRetries} attempts: ${error instanceof Error ? error.message : String(error)}`);
  }
}

function calculateAngle(a: number[], b: number[], c: number[]): number {
  const radians = Math.atan2(c[1] - b[1], c[0] - b[0]) -
                 Math.atan2(a[1] - b[1], a[0] - b[0]);
  let angle = Math.abs(radians * 180.0 / Math.PI);

  if (angle > 180.0) {
    angle = 360 - angle;
  }
  return angle;
}

function normalizeCoordinates(x: number, y: number, videoWidth: number, videoHeight: number): { x: number; y: number } {
  // CRITICAL: Log what we're receiving
  if (Math.random() < 0.01) { // Log 1% of calls to avoid spam
    console.log('normalizeCoordinates input:', { x, y, videoWidth, videoHeight });
  }

  // IMPORTANT: BlazePose returns coordinates in PIXEL space (0 to videoWidth/Height)
  // We need to normalize to 0-100 for our SVG rendering

  // Validate input coordinates and video dimensions
  if (!isFinite(x) || !isFinite(y)) {
    console.warn('Invalid coordinates detected:', { x, y, videoWidth, videoHeight });
    return { x: 50, y: 50 }; // Return center as fallback
  }

  // Validate video dimensions
  if (!isFinite(videoWidth) || !isFinite(videoHeight) || videoWidth <= 0 || videoHeight <= 0) {
    console.warn('Invalid video dimensions:', { videoWidth, videoHeight });
    return { x: 50, y: 50 }; // Return center as fallback
  }

  // Safety check for already normalized coordinates (0-100 range)
  // This might happen if data is coming from database
  if (x >= 0 && x <= 100 && y >= 0 && y <= 100 &&
      Math.max(videoWidth, videoHeight) > 100) { // Ensure video dimensions are reasonable
    console.warn('Coordinates appear to be already normalized:', { x, y });
    return { x, y };
  }

  // Normalize pixel coordinates to percentage (0-100)
  const normalizedX = (x / videoWidth) * 100;
  const normalizedY = (y / videoHeight) * 100;

  // Handle edge cases where coordinates might be outside video bounds
  // This can happen with pose estimation at video edges
  const clampedX = Math.max(0, Math.min(100, normalizedX));
  const clampedY = Math.max(0, Math.min(100, normalizedY));

  // Log warning if coordinates were outside bounds (indicates potential detection issues)
  if (normalizedX < 0 || normalizedX > 100 || normalizedY < 0 || normalizedY > 100) {
    console.warn('Pose coordinates outside video bounds - clamping:', {
      original: { x: normalizedX.toFixed(1), y: normalizedY.toFixed(1) },
      clamped: { x: clampedX.toFixed(1), y: clampedY.toFixed(1) },
      pixelCoords: { x, y },
      videoDimensions: { width: videoWidth, height: videoHeight }
    });
  }

  // Clamp to valid range
  return {
    x: clampedX,
    y: clampedY
  };
}

function getKeypointByName(keypoints: any[], name: string): any | null {
  // Enhanced keypoint detection debugging for critical joints
  if (['left_shoulder', 'right_shoulder', 'left_hip', 'right_hip', 'left_knee', 'right_knee'].includes(name)) {
    console.log(`🔍 Keypoint Detection Debug - Looking for ${name}:`, {
      total_keypoints: keypoints.length,
      model_type: keypoints.length === 17 ? 'MoveNet' : 'BlazePose',
      available_keypoints: keypoints.map((kp, i) => ({
        index: i,
        name: kp.name || `keypoint_${i}`,
        score: kp.score?.toFixed(3) || 'N/A',
        coordinates: { x: kp.x?.toFixed(1), y: kp.y?.toFixed(1) }
      })).slice(0, 10), // Show first 10 keypoints to avoid console spam
      searching_for: name
    });
  }

  // Try to detect model type based on keypoint structure
  const isMoveNet = keypoints.length === 17;

  let keypointMap: { [key: string]: number };

  if (isMoveNet) {
    // MoveNet keypoint mapping (17 keypoints)
    keypointMap = {
      'nose': 0,
      'left_eye': 1,
      'right_eye': 2,
      'left_ear': 3,
      'right_ear': 4,
      'left_shoulder': 5,
      'right_shoulder': 6,
      'left_elbow': 7,
      'right_elbow': 8,
      'left_wrist': 9,
      'right_wrist': 10,
      'left_hip': 11,
      'right_hip': 12,
      'left_knee': 13,
      'right_knee': 14,
      'left_ankle': 15,
      'right_ankle': 16,
      // Map missing keypoints to closest alternatives
      'left_foot_index': 15, // Use ankle as fallback
      'right_foot_index': 16 // Use ankle as fallback
    };
  } else {
    // BlazePose keypoint mapping (33 keypoints)
    keypointMap = {
      'nose': 0,
      'left_eye_inner': 1,
      'left_eye': 2,
      'left_eye_outer': 3,
      'right_eye_inner': 4,
      'right_eye': 5,
      'right_eye_outer': 6,
      'left_ear': 7,
      'right_ear': 8,
      'mouth_left': 9,
      'mouth_right': 10,
      'left_shoulder': 11,
      'right_shoulder': 12,
      'left_elbow': 13,
      'right_elbow': 14,
      'left_wrist': 15,
      'right_wrist': 16,
      'left_pinky': 17,
      'right_pinky': 18,
      'left_index': 19,
      'right_index': 20,
      'left_thumb': 21,
      'right_thumb': 22,
      'left_hip': 23,
      'right_hip': 24,
      'left_knee': 25,
      'right_knee': 26,
      'left_ankle': 27,
      'right_ankle': 28,
      'left_heel': 29,
      'right_heel': 30,
      'left_foot_index': 31,
      'right_foot_index': 32
    };
  }

  const index = keypointMap[name];
  if (index !== undefined && keypoints[index]) {
    const keypoint = keypoints[index];

    // Enhanced logging for critical joints with detection result
    if (['left_shoulder', 'right_shoulder', 'left_hip', 'right_hip', 'left_knee', 'right_knee'].includes(name)) {
      console.log(`✅ Keypoint Found - ${name} (${isMoveNet ? 'MoveNet' : 'BlazePose'}):`, {
        index: index,
        score: keypoint.score?.toFixed(3),
        coordinates: { x: keypoint.x?.toFixed(1), y: keypoint.y?.toFixed(1) },
        threshold_check: keypoint.score > 0.1 ? 'PASS' : 'FAIL',
        confidence_level: keypoint.score > 0.7 ? 'HIGH' : keypoint.score > 0.3 ? 'MEDIUM' : 'LOW'
      });
    }

    if (keypoint.score > 0.1) { // Very low threshold for better detection
      return keypoint;
    } else {
      // Log when keypoint is found but fails confidence threshold
      if (['left_shoulder', 'right_shoulder', 'left_hip', 'right_hip', 'left_knee', 'right_knee'].includes(name)) {
        console.log(`⚠️ Keypoint Below Threshold - ${name}: score=${keypoint.score?.toFixed(3)} < 0.1`);
      }
    }
  } else {
    // Log when keypoint is not found in the model
    if (['left_shoulder', 'right_shoulder', 'left_hip', 'right_hip', 'left_knee', 'right_knee'].includes(name)) {
      console.log(`❌ Keypoint Not Found - ${name}: index=${index}, available=${index !== undefined ? 'exists' : 'missing'}`);
    }
  }
  return null;
}

export async function analyzePose(source: HTMLVideoElement | HTMLCanvasElement, userHeightMeters: number = 1.78): Promise<PoseAnalysis3D> {
  try {
    // Check if source is video or canvas and get dimensions accordingly
    let width: number, height: number;

    if (source instanceof HTMLVideoElement) {
      // Enhanced video readiness check
      if (!source.videoWidth || !source.videoHeight) {
        throw new Error('Video dimensions not available - video may not be loaded');
      }

      if (source.readyState < 2) {
        throw new Error('Video not ready for analysis - readyState: ' + source.readyState);
      }

      width = source.videoWidth;
      height = source.videoHeight;

      console.log('Video ready for pose analysis:', {
        width: source.videoWidth,
        height: source.videoHeight,
        currentTime: source.currentTime,
        duration: source.duration,
        readyState: source.readyState,
        paused: source.paused,
        ended: source.ended
      });
    } else {
      // Canvas element
      width = source.width;
      height = source.height;

      if (!width || !height) {
        throw new Error('Canvas dimensions not available');
      }

      console.log('Canvas ready for pose analysis:', {
        width: source.width,
        height: source.height
      });
    }

    if (!detector) {
      console.log('Detector not initialized, initializing now...');
      detector = await initializePoseDetection();
    }

    // DETECTOR INSTANCE DEBUG: Track detector reuse and initialization
    console.log('🔧 BlazePose Detector Debug:', {
      detector_exists: !!detector,
      detector_instance: detector,
      detector_type: detector?.constructor?.name || 'Unknown',
      is_same_as_previous: (window as any).lastDetector === detector,
      detector_reused: (window as any).lastDetector ? ((window as any).lastDetector === detector) : 'First initialization',
      timestamp: new Date().toISOString()
    });

    // Store reference for comparison in subsequent calls
    (window as any).lastDetector = detector;

    // Create a tensor from the source element for validation
    const sourceTensor = tf.browser.fromPixels(source);
    console.log('Source tensor shape:', sourceTensor.shape);

    if (!sourceTensor.shape || sourceTensor.shape[0] === 0 || sourceTensor.shape[1] === 0) {
      sourceTensor.dispose();
      throw new Error('Invalid source dimensions for pose analysis');
    }

    // Clean up tensor immediately after validation
    sourceTensor.dispose();

    console.log('Estimating poses with detector...');
    const startTime = performance.now();

    // Enhanced BlazePose configuration for professional skeletal tracking
    const estimationConfig: any = {
      maxPoses: 1,                    // Single-person detection for running analysis
      flipHorizontal: false          // Maintain correct orientation for side-view videos
    };

    // Note: Timestamp is now passed as third parameter to estimatePoses() for proper temporal smoothing

    // Portrait video orientation detection for iPhone video optimization
    const isPortrait = source instanceof HTMLVideoElement ?
      source.videoHeight > source.videoWidth :
      source.height > source.width;

    const videoDimensions = {
      width: source instanceof HTMLVideoElement ? source.videoWidth : source.width,
      height: source instanceof HTMLVideoElement ? source.videoHeight : source.height,
      aspectRatio: source instanceof HTMLVideoElement ?
        (source.videoWidth / source.videoHeight).toFixed(2) :
        (source.width / source.height).toFixed(2)
    };

    if (isPortrait) {
      console.log('📱 Portrait video detected - optimized for iPhone analysis:', videoDimensions);
    } else {
      console.log('🖥️ Landscape video detected:', videoDimensions);
    }

    console.log(`Video orientation: ${isPortrait ? 'PORTRAIT' : 'LANDSCAPE'} (${videoDimensions.width}x${videoDimensions.height})`);

    // CRITICAL FIX: Calculate timestamp for BlazePose temporal smoothing
    const timestamp = source instanceof HTMLVideoElement ?
      source.currentTime * 1000000 : // Convert video time to microseconds for BlazePose
      Date.now() * 1000; // Use current time in microseconds for static images/canvas

    console.log('🕒 BlazePose Temporal Smoothing Setup:', {
      source_type: source instanceof HTMLVideoElement ? 'VIDEO' : 'CANVAS',
      video_time: source instanceof HTMLVideoElement ? source.currentTime.toFixed(3) + 's' : 'N/A',
      timestamp_microseconds: timestamp,
      temporal_smoothing_enabled: true,
      fps_sync: source instanceof HTMLVideoElement ? 'Video timeline synchronized' : 'Real-time processing'
    });

    console.log('Enhanced BlazePose configuration:', estimationConfig);

    // DIAGNOSTIC: Test if stuck ROI is causing coordinate drift issues
    // This randomly resets the detector to clear any accumulated state
    if (Math.random() < 0.1) { // Reset detector on 10% of frames for testing
      try {
        if (detector && typeof detector.reset === 'function') {
          detector.reset(); // Clear BlazePose detector ROI and internal state
          console.log('🔄 DIAGNOSTIC: Forced BlazePose detector reset to clear potential stuck ROI', {
            frame_context: source instanceof HTMLVideoElement ? `video time: ${source.currentTime.toFixed(3)}s` : 'canvas processing',
            reset_reason: 'Testing if ROI persistence causes coordinate drift',
            expected_effect: 'Should improve coordinate consistency if ROI was stuck',
            reset_probability: '10% of frames',
            detector_state: 'Cleared - fresh detection on next frame'
          });
        } else {
          console.log('⚠️ DIAGNOSTIC: Detector reset method not available', {
            detector_type: detector?.constructor?.name || 'Unknown',
            available_methods: detector ? Object.getOwnPropertyNames(detector as any).filter(prop => typeof (detector as any)[prop] === 'function') : [],
            alternative_approach: 'May need to reinitialize detector instead of reset'
          });
        }
      } catch (error) {
        console.log('❌ DIAGNOSTIC: Detector reset failed:', {
          error: error instanceof Error ? error.message : String(error),
          detector_state: 'Reset attempt failed - continuing with existing state',
          impact: 'No change to detector ROI - will use accumulated state'
        });
      }
    }

    // CRITICAL: Pass timestamp as third parameter to enable BlazePose temporal filtering
    const poses = await detector.estimatePoses(
      source,
      estimationConfig,
      timestamp // CRITICAL: Third parameter enables BlazePose temporal smoothing
    );

    const endTime = performance.now();

    console.log(`Enhanced pose estimation took ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`Detected ${poses.length} poses with enhanced configuration`);

    // Enhanced temporal smoothing status logging
    console.log('✅ BlazePose Temporal Smoothing Status:', {
      enabled: true,
      timestamp_provided: timestamp,
      timestamp_microseconds: timestamp,
      smoothing_type: source instanceof HTMLVideoElement ? 'VIDEO_TIMELINE_SYNC' : 'REALTIME_PROCESSING',
      coordinate_stability: 'Enhanced - reduces jitter and improves tracking accuracy',
      synchronization: source instanceof HTMLVideoElement ? 'Aligned with video playback timeline' : 'Real-time processing'
    });

    // DIAGNOSTIC: Verify 3D keypoint availability from BlazePose 'full' model
    if (poses.length > 0) {
      const pose = poses[0];

      // Check for 3D keypoint data availability
      const has3DKeypoints = pose.keypoints3D && pose.keypoints3D.length > 0;
      const hasWorldLandmarks = (pose as any).worldLandmarks && (pose as any).worldLandmarks.length > 0;

      // Check for z-coordinate availability in regular keypoints (TFJS BlazePose feature)
      const hasZCoordinates = pose.keypoints.some(kp => kp.z !== undefined && kp.z !== null);

      console.log('🌐 3D Keypoint Detection Verification:', {
        pose_detected: true,
        keypoints_2d_count: pose.keypoints?.length || 0,
        keypoints_3d_available: has3DKeypoints,
        keypoints_3d_count: has3DKeypoints ? pose.keypoints3D!.length : 0,
        world_landmarks_available: hasWorldLandmarks,
        world_landmarks_count: hasWorldLandmarks ? (pose as any).worldLandmarks.length : 0,
        z_coordinates_available: hasZCoordinates,
        blazepose_model_type: (has3DKeypoints || hasWorldLandmarks || hasZCoordinates) ? 'FULL_3D_MODEL' : 'LITE_2D_MODEL',
        enhanced_tracking_capability: (has3DKeypoints || hasWorldLandmarks || hasZCoordinates) ? 'Available' : 'Limited to 2D',
        coordinate_system_impact: (has3DKeypoints || hasZCoordinates) ? 'Can use Z-depth for improved accuracy' : 'Limited to X,Y coordinates only',
        sample_z_values: pose.keypoints.slice(0, 3).map(kp => ({ name: kp.name, z: kp.z }))
      });

      // Log sample 3D keypoint data if available
      if (has3DKeypoints && pose.keypoints3D && pose.keypoints3D.length > 0) {
        const sampleKeypoint = pose.keypoints3D[0];
        console.log('📊 Sample 3D Keypoint Data:', {
          keypoint_name: sampleKeypoint.name || 'unknown',
          x_coordinate: sampleKeypoint.x,
          y_coordinate: sampleKeypoint.y,
          z_coordinate: sampleKeypoint.z,
          confidence_score: sampleKeypoint.score,
          coordinate_system: 'BlazePose 3D world coordinates',
          depth_information: 'Z-axis provides depth relative to camera'
        });
      }

      // Check world landmarks if available
      if (hasWorldLandmarks) {
        const sampleWorldLandmark = (pose as any).worldLandmarks[0];
        console.log('🌍 Sample World Landmark Data:', {
          landmark_index: 0,
          x_world: sampleWorldLandmark.x,
          y_world: sampleWorldLandmark.y,
          z_world: sampleWorldLandmark.z,
          visibility: sampleWorldLandmark.visibility,
          coordinate_system: 'Real-world 3D coordinates (meters)',
          depth_accuracy: 'High precision depth information'
        });
      }

      // Check if we should upgrade our coordinate processing to use 3D data
      if (has3DKeypoints || hasWorldLandmarks || hasZCoordinates) {
        console.log('✅ ENHANCEMENT OPPORTUNITY: 3D keypoint data available for improved skeletal tracking', {
          current_usage: 'Using 2D coordinates only',
          enhancement_potential: '3D depth data can improve accuracy',
          implementation_suggestion: 'Consider integrating Z-coordinates for better pose analysis',
          coordinate_system_upgrade: 'Can enhance skeletal overlay with depth information',
          available_3d_features: {
            keypoints_3d: has3DKeypoints,
            world_landmarks: hasWorldLandmarks,
            z_coordinates: hasZCoordinates
          }
        });
      } else {
        console.log('⚠️ LIMITATION: Only 2D keypoints available - consider upgrading BlazePose model configuration', {
          current_model: 'Appears to be using lite or 2D-only model',
          upgrade_suggestion: 'Switch to BlazePose full model for 3D capabilities',
          coordinate_limitation: 'Limited to X,Y coordinates without depth',
          accuracy_impact: 'May affect skeletal overlay precision'
        });
      }
    } else {
      console.log('❌ 3D Keypoint Verification: No poses detected - cannot check 3D capabilities');
    }

    // COORDINATE SYSTEM VALIDATION - Critical for verifying coordinate normalization fix
    if (poses.length > 0) {
      const pose = poses[0];
      const keypoints = pose.keypoints;

      // Log raw BlazePose coordinate system to verify pixel vs normalized output
      const validKeypoints = keypoints.filter(kp => isFinite(kp.x) && isFinite(kp.y));
      const xCoords = validKeypoints.map(kp => kp.x);
      const yCoords = validKeypoints.map(kp => kp.y);

      // Use videoDimensions object already created above for orientation detection

      console.log('🔍 BlazePose Coordinate System Validation:', {
        sample_keypoint: {
          name: keypoints[0]?.name || 'unknown',
          x: keypoints[0]?.x,
          y: keypoints[0]?.y,
          score: keypoints[0]?.score
        },
        coordinate_ranges: {
          x_min: Math.min(...xCoords).toFixed(2),
          x_max: Math.max(...xCoords).toFixed(2),
          y_min: Math.min(...yCoords).toFixed(2),
          y_max: Math.max(...yCoords).toFixed(2)
        },
        video_dimensions: videoDimensions,
        video_orientation: {
          is_portrait: isPortrait,
          orientation: isPortrait ? 'PORTRAIT' : 'LANDSCAPE',
          optimized_for_iphone: isPortrait
        },
        coordinate_analysis: {
          appears_normalized: Math.max(...xCoords) <= 1 && Math.max(...yCoords) <= 1,
          appears_pixel_based: Math.max(...xCoords) > 1 || Math.max(...yCoords) > 1,
          coordinate_system: Math.max(...xCoords) > 1 || Math.max(...yCoords) > 1 ? 'PIXEL_COORDINATES' : 'NORMALIZED_COORDINATES',
          total_valid_keypoints: validKeypoints.length,
          total_keypoints: keypoints.length
        },
        normalization_fix_status: 'All coordinates will be treated as pixels and normalized to 0-100 percentage'
      });
    }

    // PHASE 2: Extract 3D world landmarks from BlazePose
    let worldLandmarks = createDefaultWorldLandmarks();
    let estimatedDistanceMeters = 2.0; // Default distance
    let depthScaleFactor = 1.0;
    let pixelsPerMeter = 100; // Default conversion
    let heightCalibrated = false;

    if (poses.length > 0) {
      const pose = poses[0];
      const hasWorldLandmarks = (pose as any).worldLandmarks && (pose as any).worldLandmarks.length > 0;

      if (hasWorldLandmarks) {
        const landmarks = (pose as any).worldLandmarks;
        console.log('🌍 Extracting 3D World Landmarks for Enhanced Analysis:', {
          total_landmarks: landmarks.length,
          user_height_meters: userHeightMeters,
          calibration_enabled: true
        });

        // Extract bilateral 3D keypoints using BlazePose world landmark indices
        worldLandmarks = {
          // Primary keypoints
          hip: extractWorldLandmark(landmarks, 23), // left_hip as primary
          knee: extractWorldLandmark(landmarks, 25), // left_knee as primary
          ankle: extractWorldLandmark(landmarks, 27), // left_ankle as primary
          shoulder: extractWorldLandmark(landmarks, 11), // left_shoulder as primary
          elbow: extractWorldLandmark(landmarks, 13), // left_elbow as primary
          wrist: extractWorldLandmark(landmarks, 15), // left_wrist as primary
          neck: extractWorldLandmark(landmarks, 0), // nose as neck approximation
          heel: extractWorldLandmark(landmarks, 29), // left_heel as primary
          foot: extractWorldLandmark(landmarks, 31), // left_foot_index as primary
          // Bilateral landmarks
          hipLeft: extractWorldLandmark(landmarks, 23),
          hipRight: extractWorldLandmark(landmarks, 24),
          kneeLeft: extractWorldLandmark(landmarks, 25),
          kneeRight: extractWorldLandmark(landmarks, 26),
          ankleLeft: extractWorldLandmark(landmarks, 27),
          ankleRight: extractWorldLandmark(landmarks, 28)
        };

        // Calculate height calibration using shoulder-to-hip distance
        const leftShoulder3D = extractWorldLandmark(landmarks, 11);
        const leftHip3D = extractWorldLandmark(landmarks, 23);

        if (leftShoulder3D.confidence > 0.3 && leftHip3D.confidence > 0.3) {
          const shoulderHipDistance = Math.sqrt(
            Math.pow(leftShoulder3D.x - leftHip3D.x, 2) +
            Math.pow(leftShoulder3D.y - leftHip3D.y, 2) +
            Math.pow(leftShoulder3D.z - leftHip3D.z, 2)
          );

          // Typical shoulder-to-hip ratio is about 0.3 of total height
          const expectedShoulderHipRatio = 0.3;
          const expectedShoulderHipDistance = userHeightMeters * expectedShoulderHipRatio;

          if (shoulderHipDistance > 0.1) { // Sanity check
            depthScaleFactor = expectedShoulderHipDistance / shoulderHipDistance;
            heightCalibrated = true;

            console.log('📏 Height Calibration Successful:', {
              measured_shoulder_hip_distance: shoulderHipDistance.toFixed(3),
              expected_shoulder_hip_distance: expectedShoulderHipDistance.toFixed(3),
              depth_scale_factor: depthScaleFactor.toFixed(3),
              user_height_meters: userHeightMeters,
              calibration_confidence: Math.min(leftShoulder3D.confidence, leftHip3D.confidence).toFixed(3)
            });
          }
        }

        // Calculate real-world metrics
        const validLandmarks = Object.values(worldLandmarks)
          .filter((lm): lm is WorldLandmark3D => (lm as WorldLandmark3D).confidence > 0.1);

        const avgZDepth = validLandmarks.length > 0
          ? validLandmarks.reduce((sum, lm) => sum + lm.z, 0) / validLandmarks.length
          : 2.0; // Default depth

        estimatedDistanceMeters = Math.abs(avgZDepth) * depthScaleFactor;
        pixelsPerMeter = width / (estimatedDistanceMeters * 0.5); // Rough estimation based on field of view

        console.log('🎯 3D World Coordinate Metrics:', {
          estimated_distance_meters: estimatedDistanceMeters.toFixed(2),
          depth_scale_factor: depthScaleFactor.toFixed(3),
          pixels_per_meter: pixelsPerMeter.toFixed(1),
          height_calibrated: heightCalibrated,
          avg_z_depth: avgZDepth.toFixed(3),
          world_landmarks_extracted: Object.keys(worldLandmarks).length
        });
      } else {
        console.log('⚠️ No 3D world landmarks available - using default values');
      }
    }

    if (poses.length === 0) {
      console.log('No poses detected - this could be due to:');
      console.log('1. No person visible in the current frame');
      console.log('2. Person is too small or partially occluded');
      console.log('3. Poor lighting or video quality');
      console.log('4. Model confidence threshold too high');
      throw new Error('No pose detected in current frame');
    }

    const pose = poses[0];
    const keypoints = pose.keypoints;

    console.log(`Pose has ${keypoints?.length || 0} keypoints`);

    if (!keypoints || keypoints.length === 0) {
      throw new Error('No keypoints detected in pose');
    }

    // Detect model type and log keypoint scores for debugging
    const isMoveNet = keypoints.length === 17;
    console.log(`Using ${isMoveNet ? 'MoveNet' : 'BlazePose'} model with ${keypoints.length} keypoints`);

    const importantKeypoints = ['left_hip', 'right_hip', 'left_knee', 'right_knee', 'left_ankle', 'right_ankle'];
    console.log('Important keypoint scores:');
    importantKeypoints.forEach(name => {
      const kp = getKeypointByName(keypoints, name);
      console.log(`  ${name}: ${kp ? kp.score.toFixed(3) : 'not detected'}`);
    });

    // Get dimensions for coordinate normalization
    const videoWidth = width;
    const videoHeight = height;

    // Get comprehensive body keypoints for enhanced anatomical analysis
    const leftShoulder = getKeypointByName(keypoints, 'left_shoulder');
    const rightShoulder = getKeypointByName(keypoints, 'right_shoulder');
    const leftElbow = getKeypointByName(keypoints, 'left_elbow');
    const rightElbow = getKeypointByName(keypoints, 'right_elbow');
    const leftWrist = getKeypointByName(keypoints, 'left_wrist');
    const rightWrist = getKeypointByName(keypoints, 'right_wrist');
    const leftHip = getKeypointByName(keypoints, 'left_hip');
    const rightHip = getKeypointByName(keypoints, 'right_hip');
    const leftKnee = getKeypointByName(keypoints, 'left_knee');
    const rightKnee = getKeypointByName(keypoints, 'right_knee');
    const leftAnkle = getKeypointByName(keypoints, 'left_ankle');
    const rightAnkle = getKeypointByName(keypoints, 'right_ankle');
    const leftHeel = getKeypointByName(keypoints, 'left_heel');
    const rightHeel = getKeypointByName(keypoints, 'right_heel');
    const leftFoot = getKeypointByName(keypoints, 'left_foot_index');
    const rightFoot = getKeypointByName(keypoints, 'right_foot_index');
    const nose = getKeypointByName(keypoints, 'nose');

    // Log enhanced keypoint detection for debugging
    console.log('Enhanced keypoint detection status:', {
      leftShoulder: leftShoulder?.score || 0,
      rightShoulder: rightShoulder?.score || 0,
      leftElbow: leftElbow?.score || 0,
      rightElbow: rightElbow?.score || 0,
      leftWrist: leftWrist?.score || 0,
      rightWrist: rightWrist?.score || 0,
      leftHeel: leftHeel?.score || 0,
      rightHeel: rightHeel?.score || 0,
      leftFoot: leftFoot?.score || 0,
      rightFoot: rightFoot?.score || 0
    });

    // Use the most visible side (higher confidence scores)
    const useLeftSide = (leftHip?.score || 0) > (rightHip?.score || 0);
    console.log(`Using ${useLeftSide ? 'left' : 'right'} side for analysis`);
    console.log(`Left hip score: ${leftHip?.score || 0}, Right hip score: ${rightHip?.score || 0}`);

    const hip = useLeftSide ? leftHip : rightHip;
    const knee = useLeftSide ? leftKnee : rightKnee;
    const ankle = useLeftSide ? leftAnkle : rightAnkle;
    const foot = useLeftSide ? leftFoot : rightFoot;
    const heel = useLeftSide ? leftHeel : rightHeel;
    const shoulder = useLeftSide ? leftShoulder : rightShoulder;
    const elbow = useLeftSide ? leftElbow : rightElbow;
    const wrist = useLeftSide ? leftWrist : rightWrist;

    console.log('Enhanced selected keypoints:', {
      hip, knee, ankle, foot, heel, shoulder, elbow, wrist, nose,
      confidence: {
        hip: hip?.score || 0,
        knee: knee?.score || 0,
        ankle: ankle?.score || 0,
        shoulder: shoulder?.score || 0,
        elbow: elbow?.score || 0,
        wrist: wrist?.score || 0
      }
    });

    // Calculate joint angles with proper error handling
    let hipAngle = 0;
    let kneeAngle = 0;
    let ankleAngle = 0;
    let trunkAngle = 0;
    let neckAngle = 0;

    if (hip && knee && ankle) {
      hipAngle = calculateAngle(
        [shoulder?.x || hip.x, shoulder?.y || hip.y - 50], // shoulder or estimated point above hip
        [hip.x, hip.y],
        [knee.x, knee.y]
      );
    }

    if (hip && knee && ankle) {
      kneeAngle = calculateAngle(
        [hip.x, hip.y],
        [knee.x, knee.y],
        [ankle.x, ankle.y]
      );
    }

    if (knee && ankle && foot) {
      ankleAngle = calculateAngle(
        [knee.x, knee.y],
        [ankle.x, ankle.y],
        [foot?.x || ankle.x, foot?.y || ankle.y + 20] // foot or estimated point below ankle
      );
    }

    if (shoulder && hip) {
      // Calculate trunk angle relative to vertical
      const deltaX = hip.x - shoulder.x;
      const deltaY = hip.y - shoulder.y;
      trunkAngle = Math.abs(Math.atan2(deltaX, deltaY) * 180 / Math.PI);
    }

    if (nose && shoulder) {
      // Calculate neck angle
      const deltaX = shoulder.x - nose.x;
      const deltaY = shoulder.y - nose.y;
      neckAngle = Math.abs(Math.atan2(deltaX, deltaY) * 180 / Math.PI);
    }

    // Debug coordinate values before normalization
    // BlazePose returns pixel coordinates, not normalized coordinates
    if (hip) {
      console.log('BlazePose hip pixel coordinates:', {
        x: hip.x,
        y: hip.y,
        confidence: hip.score,
        videoWidth,
        videoHeight
      });
    }
    if (knee) {
      console.log('BlazePose knee pixel coordinates:', {
        x: knee.x,
        y: knee.y,
        confidence: knee.score,
        videoWidth,
        videoHeight
      });
    }

    // Normalize pixel coordinates to percentage for overlay positioning
    const normalizedHip = hip ? normalizeCoordinates(hip.x, hip.y, videoWidth, videoHeight) : { x: 50, y: 50 };
    const normalizedKnee = knee ? normalizeCoordinates(knee.x, knee.y, videoWidth, videoHeight) : { x: 50, y: 70 };
    const normalizedAnkle = ankle ? normalizeCoordinates(ankle.x, ankle.y, videoWidth, videoHeight) : { x: 50, y: 90 };
    const normalizedShoulder = shoulder ? normalizeCoordinates(shoulder.x, shoulder.y, videoWidth, videoHeight) : { x: 50, y: 30 };
    const normalizedNose = nose ? normalizeCoordinates(nose.x, nose.y, videoWidth, videoHeight) : { x: 50, y: 10 };

    // Debug normalized coordinates (converted from pixels to percentages)
    console.log('Normalized coordinates (pixel → percentage):', {
      hip: normalizedHip,
      knee: normalizedKnee,
      ankle: normalizedAnkle,
      shoulder: normalizedShoulder,
      nose: normalizedNose
    });

    // Calculate basic metrics
    const strideLength = hip && ankle ? Math.abs(hip.x - ankle.x) / videoWidth * 2.0 : 1.32; // Rough estimation
    const postureScore = Math.max(0, Math.min(100, 100 - trunkAngle)); // Better posture = lower trunk angle

    // Calculate detection quality based on keypoint confidence
    const detectionQuality = Math.round(((hip?.score || 0) + (knee?.score || 0) + (ankle?.score || 0)) / 3 * 100);

    // Calculate bilateral symmetry if both sides detected
    const bilateralSymmetry = (leftHip && rightHip && leftKnee && rightKnee) ?
      Math.round((1 - Math.abs((leftHip.score + leftKnee.score) - (rightHip.score + rightKnee.score)) / 2) * 100) :
      undefined;

    // Create enhanced analysis results with 3D world coordinates
    const result = {
      angles: {
        hip: {
          angle: hipAngle,
          position: normalizedHip
        },
        knee: {
          angle: kneeAngle,
          position: normalizedKnee
        },
        ankle: {
          angle: ankleAngle,
          position: normalizedAnkle
        },
        trunk: {
          angle: trunkAngle,
          position: normalizedShoulder
        },
        neck: {
          angle: neckAngle,
          position: normalizedNose
        },
        // Enhanced anatomical angles
        shoulder: shoulder ? {
          angle: 0, // Could calculate shoulder angle if needed
          position: normalizeCoordinates(shoulder.x, shoulder.y, videoWidth, videoHeight),
          confidence: shoulder.score,
          detected: true
        } : undefined,
        elbow: elbow && shoulder && wrist ? {
          angle: calculateAngle(
            [shoulder.x, shoulder.y],
            [elbow.x, elbow.y],
            [wrist.x, wrist.y]
          ),
          position: normalizeCoordinates(elbow.x, elbow.y, videoWidth, videoHeight),
          confidence: elbow.score,
          detected: true
        } : undefined,
        wrist: wrist ? {
          angle: 0, // Could calculate wrist angle if needed
          position: normalizeCoordinates(wrist.x, wrist.y, videoWidth, videoHeight),
          confidence: wrist.score,
          detected: true
        } : undefined
      },
      keypoints: {
        // Core detected joints
        hip: { x: normalizedHip.x, y: normalizedHip.y, confidence: hip?.score || 0 },
        knee: { x: normalizedKnee.x, y: normalizedKnee.y, confidence: knee?.score || 0 },
        ankle: { x: normalizedAnkle.x, y: normalizedAnkle.y, confidence: ankle?.score || 0 },
        trunk: { x: normalizedShoulder.x, y: normalizedShoulder.y, confidence: shoulder?.score || 0 },
        neck: { x: normalizedNose.x, y: normalizedNose.y, confidence: nose?.score || 0 },
        // Enhanced keypoints from BlazePose
        shoulder: shoulder ? {
          x: normalizeCoordinates(shoulder.x, shoulder.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(shoulder.x, shoulder.y, videoWidth, videoHeight).y,
          confidence: shoulder.score
        } : undefined,
        elbow: elbow ? {
          x: normalizeCoordinates(elbow.x, elbow.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(elbow.x, elbow.y, videoWidth, videoHeight).y,
          confidence: elbow.score
        } : undefined,
        wrist: wrist ? {
          x: normalizeCoordinates(wrist.x, wrist.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(wrist.x, wrist.y, videoWidth, videoHeight).y,
          confidence: wrist.score
        } : undefined,
        heel: heel ? {
          x: normalizeCoordinates(heel.x, heel.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(heel.x, heel.y, videoWidth, videoHeight).y,
          confidence: heel.score
        } : undefined,
        foot: foot ? {
          x: normalizeCoordinates(foot.x, foot.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(foot.x, foot.y, videoWidth, videoHeight).y,
          confidence: foot.score
        } : undefined,
        // Bilateral keypoints
        shoulderLeft: leftShoulder ? {
          x: normalizeCoordinates(leftShoulder.x, leftShoulder.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(leftShoulder.x, leftShoulder.y, videoWidth, videoHeight).y,
          confidence: leftShoulder.score
        } : undefined,
        shoulderRight: rightShoulder ? {
          x: normalizeCoordinates(rightShoulder.x, rightShoulder.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(rightShoulder.x, rightShoulder.y, videoWidth, videoHeight).y,
          confidence: rightShoulder.score
        } : undefined,
        elbowLeft: leftElbow ? {
          x: normalizeCoordinates(leftElbow.x, leftElbow.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(leftElbow.x, leftElbow.y, videoWidth, videoHeight).y,
          confidence: leftElbow.score
        } : undefined,
        elbowRight: rightElbow ? {
          x: normalizeCoordinates(rightElbow.x, rightElbow.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(rightElbow.x, rightElbow.y, videoWidth, videoHeight).y,
          confidence: rightElbow.score
        } : undefined,
        wristLeft: leftWrist ? {
          x: normalizeCoordinates(leftWrist.x, leftWrist.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(leftWrist.x, leftWrist.y, videoWidth, videoHeight).y,
          confidence: leftWrist.score
        } : undefined,
        wristRight: rightWrist ? {
          x: normalizeCoordinates(rightWrist.x, rightWrist.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(rightWrist.x, rightWrist.y, videoWidth, videoHeight).y,
          confidence: rightWrist.score
        } : undefined,
        hipLeft: leftHip ? {
          x: normalizeCoordinates(leftHip.x, leftHip.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(leftHip.x, leftHip.y, videoWidth, videoHeight).y,
          confidence: leftHip.score
        } : undefined,
        hipRight: rightHip ? {
          x: normalizeCoordinates(rightHip.x, rightHip.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(rightHip.x, rightHip.y, videoWidth, videoHeight).y,
          confidence: rightHip.score
        } : undefined,
        kneeLeft: leftKnee ? {
          x: normalizeCoordinates(leftKnee.x, leftKnee.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(leftKnee.x, leftKnee.y, videoWidth, videoHeight).y,
          confidence: leftKnee.score
        } : undefined,
        kneeRight: rightKnee ? {
          x: normalizeCoordinates(rightKnee.x, rightKnee.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(rightKnee.x, rightKnee.y, videoWidth, videoHeight).y,
          confidence: rightKnee.score
        } : undefined,
        ankleLeft: leftAnkle ? {
          x: normalizeCoordinates(leftAnkle.x, leftAnkle.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(leftAnkle.x, leftAnkle.y, videoWidth, videoHeight).y,
          confidence: leftAnkle.score
        } : undefined,
        ankleRight: rightAnkle ? {
          x: normalizeCoordinates(rightAnkle.x, rightAnkle.y, videoWidth, videoHeight).x,
          y: normalizeCoordinates(rightAnkle.x, rightAnkle.y, videoWidth, videoHeight).y,
          confidence: rightAnkle.score
        } : undefined
      },
      metrics: {
        strideLength: Math.round(strideLength * 100) / 100,
        footStrike: ankleAngle > 95 ? 'heel' : ankleAngle < 85 ? 'forefoot' : 'midfoot',
        postureScore: Math.round(postureScore),
        detectionQuality,
        bilateralSymmetry
      },
      // New 3D world coordinate properties
      worldLandmarks,
      estimatedDistanceMeters,
      depthScaleFactor,
      pixelsPerMeter,
      heightCalibrated
    };

    // VERIFICATION: Log 3D world coordinate output for debugging
    console.log('🔍 VERIFICATION: PoseAnalysis3D output', {
      has3DData: !!worldLandmarks.hip.z,
      estimatedDistance: estimatedDistanceMeters,
      depthScale: depthScaleFactor,
      pixelsPerMeter: pixelsPerMeter,
      heightCalibrated: heightCalibrated,
      sampleWorldLandmark: worldLandmarks.hip,
      worldLandmarksCount: Object.keys(worldLandmarks).length,
      userHeightMeters: userHeightMeters
    });

    return result;
  } catch (error) {
    console.error('Error in analyzePose:', error);
    throw error;
  }
}

// Helper function to extract world landmark
function extractWorldLandmark(landmarks: any[], index: number): WorldLandmark3D {
  if (landmarks && landmarks[index]) {
    const lm = landmarks[index];
    return {
      x: lm.x || 0,
      y: lm.y || 0,
      z: lm.z || 0,
      confidence: lm.score || lm.visibility || 0
    };
  }
  return { x: 0, y: 0, z: 2, confidence: 0 };
}

// Helper function for default world landmarks
function createDefaultWorldLandmarks(): any {
  const defaultLandmark = { x: 0, y: 0, z: 2, confidence: 0 };
  return {
    hip: defaultLandmark,
    knee: defaultLandmark,
    ankle: defaultLandmark,
    shoulder: defaultLandmark,
    elbow: defaultLandmark,
    wrist: defaultLandmark,
    neck: defaultLandmark,
    heel: defaultLandmark,
    foot: defaultLandmark,
    hipLeft: defaultLandmark,
    hipRight: defaultLandmark,
    kneeLeft: defaultLandmark,
    kneeRight: defaultLandmark,
    ankleLeft: defaultLandmark,
    ankleRight: defaultLandmark,
  };
}